/**
 * chrome-ai.d.ts
 *
 * This file provides custom type definitions for the experimental chrome.ai API.
 * This is necessary because the official @types/chrome package may not yet
 * include these new APIs, which would cause TypeScript compilation errors.
 *
 * By creating this declaration file, we can "teach" TypeScript what the
 * chrome.ai object and its methods look like, enabling type safety and
 * autocompletion.
 */

// Define the structure of the TextSession object returned by createTextSession
interface AiTextSession {
  /**
   * Sends a prompt to the on-device model.
   * @param prompt The text prompt to send to the model.
   * @returns A promise that resolves with the model's response string.
   */
  prompt(prompt: string): Promise<string>;

  /**
   * Frees the resources associated with this session.
   */
  destroy(): void;
}

// Use declaration merging to add the 'ai' property to the existing 'chrome' namespace
declare namespace chrome {
  namespace ai {
    /**
     * Checks if the browser is capable of creating a text session.
     * @returns A promise that resolves to "no", "after-download", or "readily".
     */
    function canCreateTextSession(): Promise<string>;

    /**
     * Creates a new text session for interacting with the on-device AI model.
     * @returns A promise that resolves with a TextSession object.
     */
    function createTextSession(): Promise<AiTextSession>;
  }
}
