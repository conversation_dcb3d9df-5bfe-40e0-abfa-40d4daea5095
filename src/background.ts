/**
 * background.ts
 *
 * This is the core service worker for the extension. It handles the main logic:
 * 1.  Receives messages from the popup.
 * 2.  Reads all open tabs.
 * 3.  Gathers metadata for each tab.
 * 4.  Uses the chrome.ai API to classify and group tabs.
 * 5.  Saves the grouped bookmarks to chrome.storage.local.
 */

// Define the structure for our smart bookmark objects
interface SmartBookmark {
    url: string;
    title: string;
    description: string;
    lastAccessed: number;
    bookmarkedAt: number;
    visitCount: number;
    notes: string;
    favIconUrl?: string;
}

// Listen for the message from the popup script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "bookmarkAllTabs") {
        // Use an async function to handle the process and respond when done
        processAndSaveTabs().then(() => {
            sendResponse({ status: "Processing complete" });
        }).catch(error => {
            console.error("Error processing tabs:", error);
            sendResponse({ status: "Error", message: error.message });
        });
        return true; // Indicates we will send a response asynchronously
    }
});

async function processAndSaveTabs() {
    console.log("Starting tab processing...");

    // 1. Get all tabs from the current window that are not system pages
    const tabs = await chrome.tabs.query({ currentWindow: true, url: ["http://*/*", "https://*/*"] });
    
    // Check if the AI model is available
    const canCreate = await chrome.ai.canCreateTextSession();
    if (canCreate !== "no" && canCreate !== "readily") {
        console.log("AI model not ready, waiting for it to become available.");
        // You could implement a retry mechanism here if needed
        return;
    }

    const aiSession = await chrome.ai.createTextSession();
    console.log("AI session created.");

    const bookmarksToGroup: SmartBookmark[] = [];

    for (const tab of tabs) {
        if (!tab.id || !tab.url || !tab.title) continue;

        // 2. Get additional info for each tab
        const description = await getMetaDescription(tab.id);
        const visitCount = await getVisitCount(tab.url);

        const bookmark: SmartBookmark = {
            url: tab.url,
            title: tab.title,
            description: description,
            lastAccessed: tab.lastAccessed || Date.now(),
            bookmarkedAt: Date.now(),
            visitCount: visitCount,
            notes: "",
            favIconUrl: tab.favIconUrl || ''
        };
        
        bookmarksToGroup.push(bookmark);
    }

    if (bookmarksToGroup.length === 0) {
        console.log("No valid tabs to bookmark.");
        aiSession.destroy();
        return;
    }

    // 3. Group the bookmarks using the AI
    const groupedBookmarks: { [category: string]: SmartBookmark[] } = {};
    const categories = ["Programming", "Travel", "Shopping", "News", "Research", "Social Media", "Entertainment", "Productivity", "Finance", "Health", "Miscellaneous"];

    // Create the prompt for the AI model
    const promptContent = bookmarksToGroup.map((bm, index) => 
        `Item ${index}: Title: "${bm.title}". Description: "${bm.description}"`
    ).join('\n');
    
    const prompt = `
        Analyze the following list of web page items. For each item, classify it into ONE of the following categories: ${categories.join(', ')}.
        Respond with a JSON array of objects, where each object has an "item" number and a "category". Do not include any other text in your response.

        ${promptContent}
    `;

    console.log("Sending prompt to AI...");
    const aiResponse = await aiSession.prompt(prompt);
    console.log("AI Response:", aiResponse);

    try {
        // Clean the response to ensure it's valid JSON
        const jsonString = aiResponse.replace(/```json\n?|\n?```/g, '').trim();
        const classifications = JSON.parse(jsonString);

        classifications.forEach((item: { item: number, category: string }) => {
            const category = item.category || "Miscellaneous";
            const bookmark = bookmarksToGroup[item.item];
            if (bookmark) {
                if (!groupedBookmarks[category]) {
                    groupedBookmarks[category] = [];
                }
                groupedBookmarks[category].push(bookmark);
            }
        });

    } catch (e) {
        console.error("Failed to parse AI response, falling back to individual classification.", e);
        // Fallback: If batch processing fails, classify one by one (slower but more robust)
        for (const bookmark of bookmarksToGroup) {
            const fallbackPrompt = `Classify this page: Title: "${bookmark.title}", Description: "${bookmark.description}" into one category: ${categories.join(', ')}. Respond with only the category name.`;
            const category = (await aiSession.prompt(fallbackPrompt)).trim() || "Miscellaneous";
            if (!groupedBookmarks[category]) {
                groupedBookmarks[category] = [];
            }
            groupedBookmarks[category].push(bookmark);
        }
    }

    // 4. Save the grouped bookmarks to local storage
    const sessionKey = `bookmarks_${Date.now()}`;
    await chrome.storage.local.set({ [sessionKey]: groupedBookmarks });
    console.log("Bookmarks saved successfully!", { [sessionKey]: groupedBookmarks });

    aiSession.destroy(); // Clean up the AI session
}

// Helper function to inject a script and get the meta description
async function getMetaDescription(tabId: number): Promise<string> {
    try {
        const results = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: () => document.querySelector('meta[name="description"]')?.getAttribute('content') || ''
        });
        // Ensure result is not undefined and is a string
        return (results && results[0] && results[0].result) || '';
    } catch (e) {
        console.warn(`Could not access content script on tab ${tabId}:`, e);
        return ''; // Return empty string on failure
    }
}

// Helper function to get the visit count from history
async function getVisitCount(url: string): Promise<number> {
    try {
        const historyItems = await chrome.history.getVisits({ url: url });
        return historyItems.length;
    } catch (e) {
        console.warn(`Could not get history for ${url}:`, e);
        return 0;
    }
}
