/**
 * popup.ts
 *
 * This script handles the logic for the extension's popup window.
 * It sends a message to the background script to initiate the bookmarking process.
 */

const bookmarkBtn = document.getElementById('bookmark-all-btn') as HTMLButtonElement;
const statusDiv = document.getElementById('status') as HTMLDivElement;

bookmarkBtn?.addEventListener('click', () => {
    statusDiv.textContent = 'Processing...';
    bookmarkBtn.disabled = true;

    // Send a message to the background script to start the main process.
    // The background script has more permissions and a longer-running context.
    chrome.runtime.sendMessage({ action: "bookmarkAllTabs" }, (response) => {
        if (chrome.runtime.lastError) {
            console.error(chrome.runtime.lastError.message);
            statusDiv.textContent = 'Error occurred.';
            bookmarkBtn.disabled = false;
        } else {
            console.log(response.status);
            statusDiv.textContent = 'Tabs bookmarked!';
            // The popup will close automatically after a short delay
            setTimeout(() => window.close(), 1500);
        }
    });
});
