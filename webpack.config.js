const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');

module.exports = {
  mode: 'production', // Use 'development' for easier debugging
  entry: {
    background: './src/background.ts',
    popup: './src/popup.ts',
  },
  output: {
    path: path.resolve(__dirname, 'dist/js'),
    filename: '[name].js',
    clean: true, // Clean the output directory before emit.
  },
  resolve: {
    extensions: ['.ts', '.js'],
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  plugins: [
    // This plugin copies files from the source to the distribution directory
    new CopyPlugin({
      patterns: [
        // Copy manifest.json to the root of the dist folder
        { from: 'manifest.json', to: path.resolve(__dirname, 'dist') },
        // Copy popup.html to the root of the dist folder
        { from: 'popup.html', to: path.resolve(__dirname, 'dist') },
        // Copy icons to the 'icons' subfolder in dist
        { from: 'icons', to: path.resolve(__dirname, 'dist/icons') },
      ],
    }),
  ],
  // Optional: Add source maps for easier debugging in development
  devtool: 'cheap-module-source-map',
};
